'use client'

import { useState } from 'react'
import { Check, X, Edit, RotateCcw, Ban, CheckCircle, Clock } from 'lucide-react'
import { DoctorWithStats } from '@/lib/types'
import { performAdminAction, resetDoctorQuota } from '@/lib/actions/admin'
import { formatDate } from '@/lib/utils'

interface DoctorsTableProps {
  doctors: DoctorWithStats[]
}

export function DoctorsTable({ doctors }: DoctorsTableProps) {
  const [loading, setLoading] = useState<string | null>(null)
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)

  const handleAction = async (
    action: 'approve' | 'reject' | 'disable' | 'enable',
    doctorId: string
  ) => {
    setLoading(doctorId)
    setMessage(null)

    try {
      const result = await performAdminAction({
        action,
        doctor_id: doctorId
      })
      
      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Doctor ${action}d successfully!` 
        })
        // Refresh the page to show updated data
        window.location.reload()
      } else {
        setMessage({ 
          type: 'error', 
          text: result.error || `Failed to ${action} doctor` 
        })
      }
    } catch (_error) {
      setMessage({ 
        type: 'error', 
        text: 'An error occurred. Please try again.' 
      })
    } finally {
      setLoading(null)
    }
  }

  const handleQuotaUpdate = async (doctorId: string) => {
    const newQuota = prompt('Enter new monthly quota:')
    if (newQuota && !isNaN(Number(newQuota))) {
      await handleAction('approve', doctorId) // This is a placeholder - you'd need a proper quota update action
    }
  }

  const handleQuotaReset = async (doctorId: string) => {
    setLoading(doctorId)
    setMessage(null)

    try {
      const result = await resetDoctorQuota(doctorId)
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Quota reset successfully!' })
        window.location.reload()
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to reset quota' })
      }
    } catch (_error) {
      setMessage({ type: 'error', text: 'An error occurred while resetting quota' })
    } finally {
      setLoading(null)
    }
  }

  const handleRejectDoctor = async (doctorId: string) => {
    if (confirm('Are you sure you want to reject this doctor? This action cannot be undone.')) {
      await handleAction('reject', doctorId)
    }
  }

  const handleDisableDoctor = async (doctorId: string) => {
    if (confirm('Are you sure you want to disable this doctor? They will not be able to log in until re-enabled.')) {
      await handleAction('disable', doctorId)
    }
  }

  const handleQuotaResetConfirm = async (doctorId: string) => {
    if (confirm('Are you sure you want to reset this doctor\'s quota usage to zero?')) {
      await handleQuotaReset(doctorId)
    }
  }



  const getStatusBadge = (doctor: DoctorWithStats) => {
    if (!doctor.approved) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          <Clock className="w-3 h-3 mr-1" />
          Pending
        </span>
      )
    }



    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
        <CheckCircle className="w-3 h-3 mr-1" />
        Active
      </span>
    )
  }

  const getQuotaDisplay = (doctor: DoctorWithStats) => {
    const percentage = doctor.quota_percentage || 0
    
    const getColor = () => {
      if (percentage >= 90) return 'text-red-600'
      if (percentage >= 70) return 'text-orange-600'
      return 'text-green-600'
    }

    return (
      <div className="text-sm">
        <div className="font-medium text-gray-900">
          {doctor.quota_used} / {doctor.monthly_quota}
        </div>
        <div className={`text-xs ${getColor()}`}>
          {percentage}% used
        </div>
      </div>
    )
  }

  return (
    <div className="overflow-hidden">
      {message && (
        <div className={`p-4 mb-4 rounded-md ${message.type === 'success' ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
          {message.text}
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Doctor
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quota
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                Activity
              </th>
              <th className="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {doctors.map((doctor) => (
              <tr key={doctor.id} className="hover:bg-gray-50">
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-gray-900">
                      {doctor.name}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-500 break-all">
                      {doctor.email}
                    </div>
                    {doctor.clinic_name ? (
                      <div className="text-xs text-gray-400 hidden sm:block">
                        {doctor.clinic_name}
                      </div>
                    ) : (
                      <div className="text-xs text-gray-400 hidden sm:block">
                        No clinic specified
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(doctor)}
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap">
                  {getQuotaDisplay(doctor)}
                </td>

                <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">
                  <div className="space-y-1">
                    <div className="text-xs">
                      Joined: {formatDate(doctor.created_at)}
                    </div>
                    <div className="text-xs">
                      Last active: {doctor.last_activity ? formatDate(doctor.last_activity) : 'Never'}
                    </div>
                  </div>
                </td>
                <td className="px-3 sm:px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex flex-wrap gap-1 sm:gap-2">
                    {!doctor.approved ? (
                      <>
                        <button
                          onClick={() => handleAction('approve', doctor.id)}
                          disabled={loading === doctor.id}
                          className="text-green-600 hover:text-green-900 disabled:opacity-50"
                          title="Approve Doctor"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleRejectDoctor(doctor.id)}
                          disabled={loading === doctor.id}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          title="Reject Doctor"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => handleQuotaUpdate(doctor.id)}
                          disabled={loading === doctor.id}
                          className="text-blue-600 hover:text-blue-900 disabled:opacity-50"
                          title="Update Quota"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleQuotaResetConfirm(doctor.id)}
                          disabled={loading === doctor.id}
                          className="text-purple-600 hover:text-purple-900 disabled:opacity-50"
                          title="Reset Quota"
                        >
                          <RotateCcw className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => handleDisableDoctor(doctor.id)}
                          disabled={loading === doctor.id}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          title="Disable Doctor"
                        >
                          <Ban className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {doctors.length === 0 && (
        <div className="text-center py-12">
          <h3 className="mt-2 text-sm font-medium text-gray-900">No doctors found</h3>
          <p className="mt-1 text-sm text-gray-500">
            No doctors have signed up yet.
          </p>
        </div>
      )}


    </div>
  )
}