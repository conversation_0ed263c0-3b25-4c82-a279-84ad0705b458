import { getUser, getDoctorQuota } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { DashboardStats } from '@/components/analytics/dashboard-stats'
import { ConsultationsList } from '@/components/analytics/consultations-list'
import { QuotaCard } from '@/components/analytics/quota-card'
import { ReferralStats } from '@/components/analytics/referral-stats'

interface InfoDataProps {
  userId: string
}

export async function InfoData({ userId }: InfoDataProps) {
  // This runs inside Suspense boundary, not blocking initial page render
  // OPTIMIZED: Load only initial page (15 items) for fast initial render
  const [user, consultationsResult, quotaInfo] = await Promise.all([
    getUser(),
    getConsultations({ page: 1, pageSize: 15 }), // Small initial load with pagination
    getDoctorQuota(userId),
  ])

  const consultations = consultationsResult.success ? consultationsResult.data.consultations || [] : []
  const hasMore = consultationsResult.success ? consultationsResult.data.hasMore : false

  // Calculate stats from consultations (same logic as before)
  const stats = {
    total_consultations: consultations.length,
    pending_consultations: consultations.filter(c => c.status === 'pending').length,
    generated_consultations: consultations.filter(c => c.status === 'generated').length,
    approved_consultations: consultations.filter(c => c.status === 'approved').length,
    today_consultations: consultations.filter(c => {
      const today = new Date().toDateString()
      const consultationDate = new Date(c.created_at).toDateString()
      return today === consultationDate
    }).length,
  }

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Dashboard Layout: 2x2 Left, Quota Middle, Referral Right */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 2x2 Stats Grid - Left (equal width) */}
        <div className="lg:col-span-1">
          <DashboardStats stats={stats} />
        </div>
        
        {/* Quota Card - Middle (equal width) */}
        <div className="lg:col-span-1">
          {quotaInfo && <QuotaCard quota={quotaInfo} doctorId={userId} />}
        </div>
        
        {/* Referral Card - Right (equal width) */}
        <div className="lg:col-span-1">
          <ReferralStats doctorId={userId} />
        </div>
      </div>

      {/* Consultations List */}
      <div className="relative">
        <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
        <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl border border-white/20">
          <div className="px-6 py-4 border-b border-white/30 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-medium text-slate-800">
                  Patient Consultations
                </h2>
                <p className="text-sm text-slate-600">
                  Review and manage patient consultation summaries
                </p>
              </div>
              <a
                href="/dashboard"
                className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
                title="Add New Recording"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </a>
            </div>
          </div>
          <ConsultationsList consultations={consultations} hasMore={hasMore} />
        </div>
      </div>
    </div>
  )
}
