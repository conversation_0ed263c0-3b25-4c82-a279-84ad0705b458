import { getUser } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { NewRecordingInterface } from '@/components/recording/new-recording-interface'

interface DashboardDataProps {
  doctorId: string
}

export async function DashboardData({ doctorId }: DashboardDataProps) {
  // This runs inside Suspense boundary, not blocking initial page render
  const [user, consultationsResult] = await Promise.all([
    getUser(),
    getConsultations(),
  ])

  const consultations = consultationsResult.success ? consultationsResult.data || [] : []

  return (
    <NewRecordingInterface
      user={user}
      consultations={consultations}
      doctorId={doctorId}
    />
  )
}
