'use client'

import { useState } from 'react'
import { Clock, FileText, CheckCircle, Eye, Wand2, Filter } from 'lucide-react'
import { Consultation } from '@/lib/types'
import { formatDate, formatRelativeTime } from '@/lib/utils'
import { ConsultationModal } from './consultation-modal'

interface ConsultationsListProps {
  consultations: Consultation[]
  doctorName?: string
}

export function ConsultationsList({ consultations, doctorName }: ConsultationsListProps) {
  const [selectedConsultation, setSelectedConsultation] = useState<Consultation | null>(null)
  const [filter, setFilter] = useState<'all' | 'pending' | 'generated' | 'approved'>('all')
  const [dateFilter, setDateFilter] = useState<'all' | 'today' | 'yesterday' | 'custom'>('all')
  const [customDate, setCustomDate] = useState<string>('')


  // Handle consultation updates from modal
  const handleConsultationUpdate = (updatedConsultation: Consultation) => {
    setSelectedConsultation(updatedConsultation)
  }

  const getDateFilteredConsultations = (consultations: Consultation[]) => {
    if (dateFilter === 'all') return consultations
    
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)
    
    return consultations.filter(consultation => {
      const consultationDate = new Date(consultation.created_at)
      
      switch (dateFilter) {
        case 'today':
          return consultationDate.toDateString() === today.toDateString()
        case 'yesterday':
          return consultationDate.toDateString() === yesterday.toDateString()
        case 'custom':
          if (!customDate) return true
          const selectedDate = new Date(customDate)
          return consultationDate.toDateString() === selectedDate.toDateString()
        default:
          return true
      }
    })
  }

  const filteredConsultations = getDateFilteredConsultations(consultations).filter(consultation => {
    if (filter === 'all') return true
    return consultation.status === filter
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-orange-600" />
      case 'generated':
        return <FileText className="w-4 h-4 text-emerald-600" />
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      default:
        return <Clock className="w-4 h-4 text-slate-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending Review'
      case 'generated':
        return 'Summary Generated'
      case 'approved':
        return 'Approved'
      default:
        return 'Unknown'
    }
  }

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-orange-100 text-orange-800 border border-orange-300'
      case 'generated':
        return 'bg-emerald-100 text-emerald-800 border border-emerald-300'
      case 'approved':
        return 'bg-green-100 text-green-800 border border-green-300'
      default:
        return 'bg-slate-100 text-slate-800 border border-slate-300'
    }
  }

  const tabKeys: Array<'all' | 'pending' | 'generated' | 'approved'> = ['all', 'pending', 'generated', 'approved']

  if (consultations.length === 0) {
    return (
      <div className="p-6 text-center">
        <FileText className="mx-auto h-12 w-12 text-slate-400" />
        <h3 className="mt-2 text-sm font-medium text-slate-800">No consultations</h3>
        <p className="mt-1 text-sm text-slate-600">
          Get started by recording a consultation on the mobile interface.
        </p>
      </div>
    )
  }

  return (
    <>
      <div className="p-6">
        {/* Filter Tabs with Date Filter on Right */}
        <div className="border-b border-orange-200 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            {/* Filter Tabs */}
            <nav className="-mb-px flex space-x-2 sm:space-x-8 overflow-x-auto">
              {tabKeys.map((key) => (
                <button
                  key={key}
                  onClick={() => setFilter(key)}
                  className={`whitespace-nowrap py-2 px-2 sm:px-1 border-b-2 font-medium text-xs sm:text-sm transition-colors duration-200 flex-shrink-0 ${
                    filter === key
                      ? 'border-teal-500 text-teal-600'
                      : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-orange-300'
                  }`}
                >
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                  {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length > 0 && (
                    <span className={`ml-1 sm:ml-2 py-0.5 px-1 sm:px-2 rounded-full text-xs ${
                      filter === key
                        ? 'bg-teal-100 text-teal-700'
                        : 'bg-orange-100 text-slate-800'
                    }`}>
                      {getDateFilteredConsultations(consultations).filter(c => key === 'all' ? true : c.status === key).length}
                    </span>
                  )}
                </button>
              ))}
            </nav>

            {/* Date Filter on Right */}
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Filter className="w-4 h-4 text-slate-600" />
              <select
                value={dateFilter}
                onChange={(e) => {
                  const value = e.target.value as 'all' | 'today' | 'yesterday' | 'custom'
                  setDateFilter(value)
                  if (value !== 'custom') {
                    setCustomDate('')
                  }
                }}
                className="text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="all">All Dates</option>
                <option value="today">Today</option>
                <option value="yesterday">Yesterday</option>
                <option value="custom">Custom Date</option>
              </select>
              
              {dateFilter === 'custom' && (
                <input
                  type="date"
                  value={customDate}
                  onChange={(e) => setCustomDate(e.target.value)}
                  className="text-sm border border-gray-300 rounded-md px-3 py-1.5 bg-white text-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-teal-500"
                />
              )}
            </div>
          </div>
        </div>

        {/* Consultations List */}
        <div className="space-y-4">
          {filteredConsultations.map((consultation) => (
            <div
              key={consultation.id}
              className="bg-white/80 backdrop-blur-sm border border-orange-200/50 rounded-lg p-3 sm:p-4 hover:shadow-lg hover:bg-white/90 transition-all duration-200 hover:scale-[1.02]"
            >
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div className="flex items-start sm:items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
                  <div className="flex-shrink-0 mt-0.5 sm:mt-0">
                    {getStatusIcon(consultation.status)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-2">
                      <h3 className="text-sm font-medium text-slate-800 truncate">
                        {consultation.patient_name || `Patient #${consultation.patient_number || 'N/A'}`}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium self-start ${getStatusBadgeClass(consultation.status)}`}>
                        {getStatusText(consultation.status)}
                      </span>
                    </div>

                    <div className="mt-1 flex flex-col sm:flex-row sm:items-center space-y-1 sm:space-y-0 sm:space-x-4 text-xs sm:text-sm text-slate-600">
                      <span className="truncate">Submitted by: {consultation.submitted_by}</span>
                      <span className="hidden sm:inline">•</span>
                      <span>{formatRelativeTime(consultation.created_at)}</span>
                      <span className="hidden sm:inline">•</span>
                      <span className="hidden sm:inline">{formatDate(consultation.created_at)}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 flex-shrink-0">
                  {/* Primary Action Button - Different for each status */}
                  <button
                    onClick={() => setSelectedConsultation(consultation)}
                    className={`inline-flex items-center px-2 sm:px-3 py-1.5 border text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 shadow-md hover:shadow-lg transition-all duration-200 ${
                      consultation.status === 'pending'
                        ? 'border-transparent text-white bg-gradient-to-r from-teal-600 to-emerald-700 hover:from-teal-700 hover:to-emerald-800 focus:ring-teal-500'
                        : consultation.status === 'generated'
                        ? 'border-transparent text-white bg-gradient-to-r from-blue-600 to-indigo-700 hover:from-blue-700 hover:to-indigo-800 focus:ring-blue-500'
                        : 'border-transparent text-white bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 focus:ring-green-500'
                    }`}
                  >
                    {consultation.status === 'pending' ? (
                      <>
                        <Wand2 className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Generate Summary</span>
                        <span className="sm:hidden">Generate</span>
                      </>
                    ) : consultation.status === 'generated' ? (
                      <>
                        <Eye className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">Review Summary</span>
                        <span className="sm:hidden">Review</span>
                      </>
                    ) : (
                      <>
                        <Eye className="w-3 h-3 mr-1" />
                        <span className="hidden sm:inline">View Approved</span>
                        <span className="sm:hidden">View</span>
                      </>
                    )}
                  </button>

                  {/* Secondary Action Button - View Details (consistent for all) */}
                  <button
                    onClick={() => setSelectedConsultation(consultation)}
                    className="inline-flex items-center px-2 sm:px-3 py-1.5 border border-orange-300 hover:border-teal-400 text-xs font-medium rounded text-slate-700 bg-white/70 hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-teal-500 transition-all duration-200"
                  >
                    <Eye className="w-3 h-3 mr-1" />
                    <span className="hidden sm:inline">View Details</span>
                    <span className="sm:hidden">Details</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredConsultations.length === 0 && filter !== 'all' && (
          <div className="text-center py-8">
            <p className="text-sm text-slate-600">
              No consultations with status &quot;{filter}&quot;.
            </p>
          </div>
        )}
      </div>

      {/* Consultation Modal */}
      {selectedConsultation && (
        <ConsultationModal
          consultation={selectedConsultation}
          onClose={() => setSelectedConsultation(null)}
          onConsultationUpdate={handleConsultationUpdate}
          doctorName={doctorName}
        />
      )}
    </>
  )
}