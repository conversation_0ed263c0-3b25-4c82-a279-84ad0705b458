import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { verifySession, getUser, getDoctorQuota } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'

import { DashboardStats } from '@/components/analytics/dashboard-stats'
import { ConsultationsList } from '@/components/analytics/consultations-list'
import { QuotaCard } from '@/components/analytics/quota-card'
import { ReferralStats } from '@/components/analytics/referral-stats'
import { Sparkles, BarChart3 } from 'lucide-react'


export const metadata: Metadata = {
  title: 'Info - Celer AI',
  description: 'View statistics, quota information, and referral details',
}

export default async function InfoPage() {
  const session = await verifySession()
  
  // Parallelize dependent calls after getting session
  const [user, consultationsResult, quotaInfo] = await Promise.all([
    getUser(),
    getConsultations(),
    getDoctorQuota(session.userId)
  ])

  const consultations = consultationsResult.success ? consultationsResult.data || [] : []

  // Calculate stats
  const stats = {
    total_consultations: consultations.length,
    pending_consultations: consultations.filter(c => c.status === 'pending').length,
    generated_consultations: consultations.filter(c => c.status === 'generated').length,
    approved_consultations: consultations.filter(c => c.status === 'approved').length,
    today_consultations: consultations.filter(c => {
      const today = new Date().toDateString()
      const consultationDate = new Date(c.created_at).toDateString()
      return today === consultationDate
    }).length,
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 overflow-x-hidden">
      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-indigo-200/20 to-purple-200/20 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-br from-cyan-200/20 to-blue-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-40 left-1/4 w-40 h-40 bg-gradient-to-br from-purple-200/10 to-pink-200/10 rounded-full blur-xl animate-pulse delay-2000"></div>
      </div>

      {/* Floating Navigation */}
      <nav className="fixed top-6 left-1/2 transform -translate-x-1/2 z-50 bg-white/80 backdrop-blur-xl rounded-full px-6 py-3 shadow-lg border border-white/20">
        <div className="flex items-center space-x-8">
          <div className="flex items-center space-x-3">
            <div className="relative w-8 h-8">
              <Image
                src="/celer-ai-logo.svg"
                alt="Celer AI"
                width={32}
                height={32}
                className="rounded-lg"
              />
            </div>
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse font-semibold">
              Celer AI
            </span>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href="/dashboard"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Dashboard
            </Link>
            <Link
              href="/settings"
              className="text-slate-600 hover:text-slate-900 text-sm font-medium transition-colors"
            >
              Settings
            </Link>
          </div>
        </div>
      </nav>

      <main className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-black text-slate-900 leading-none mb-4">
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 animate-pulse">
              Analytics
            </span>
          </h1>

          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-full px-4 py-2 mb-12">
            <BarChart3 className="w-4 h-4 text-indigo-600 animate-pulse" />
            <span className="text-indigo-700 text-sm font-medium">Analytics & Insights</span>
            <Sparkles className="w-4 h-4 text-purple-600" />
          </div>
        </div>

        <div className="space-y-6 lg:space-y-8">
          {/* Dashboard Layout: 2x2 Left, Quota Middle, Referral Right */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 2x2 Stats Grid - Left (equal width) */}
            <div className="lg:col-span-1">
              <DashboardStats stats={stats} />
            </div>
            
            {/* Quota Card - Middle (equal width) */}
            <div className="lg:col-span-1">
              {quotaInfo && <QuotaCard quota={quotaInfo} doctorId={session.userId} />}
            </div>
            
            {/* Referral Card - Right (equal width) */}
            <div className="lg:col-span-1">
              <ReferralStats doctorId={session.userId} />
            </div>
          </div>

          {/* Consultations List */}
          <div className="relative">
            <div className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-2xl blur-lg opacity-20 animate-pulse"></div>
            <div className="relative bg-white/90 backdrop-blur-xl shadow-2xl rounded-2xl border border-white/20">
              <div className="px-6 py-4 border-b border-white/30 bg-gradient-to-r from-indigo-50/50 to-purple-50/50">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-lg font-medium text-slate-800">
                      Patient Consultations
                    </h2>
                    <p className="text-sm text-slate-600">
                      Review and manage patient consultation summaries
                    </p>
                  </div>
                  <a
                    href="/dashboard"
                    className="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 active:scale-95"
                    title="Add New Recording"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                  </a>
                </div>
              </div>

            <ConsultationsList consultations={consultations} doctorName={user?.name} />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}