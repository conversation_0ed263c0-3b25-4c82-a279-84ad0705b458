import { Metadata } from 'next'
import { verifySession, getUser } from '@/lib/auth/dal'
import { getConsultations } from '@/lib/actions/consultations'
import { NewRecordingInterface } from '@/components/recording/new-recording-interface'
import { DashboardClient } from '@/components/shared/dashboard-client'
import { PWAInstallPrompt } from '@/components/pwa/pwa-install-prompt'

export const metadata: Metadata = {
  title: 'Dashboard - Celer AI',
  description: 'Create new patient consultations with AI-powered summaries',
}

export default async function DashboardPage() {
  const session = await verifySession()
  
  // Parallelize dependent calls after getting session
  const [user, consultationsResult] = await Promise.all([
    getUser(),
    getConsultations(),
  ])

  const consultations = consultationsResult.success ? consultationsResult.data || [] : []

  return (
    <>
      <NewRecordingInterface
        user={user}
        consultations={consultations}
        doctorId={session.userId}
      />

      {/* Client-side components for modals */}
      <DashboardClient doctorId={session.userId} />

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </>
  )
}
