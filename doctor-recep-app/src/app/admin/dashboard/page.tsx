import { getAdmin } from '@/lib/auth/admin-dal'
import { getAdminDashboardStats, getAllDoctorsWithStats } from '@/lib/actions/admin'
import { AdminDashboardHeader } from '@/components/admin/admin-dashboard-header'
import { AdminDashboardStats } from '@/components/admin/admin-dashboard-stats'
import { DoctorsTable } from '@/components/admin/doctors-table'
import { BillingManagement } from '@/components/admin/billing-management'
import { redirect } from 'next/navigation'

export default async function AdminDashboardPage({ 
  searchParams 
}: { 
  searchParams: Promise<{ tab?: string }> 
}) {
  const startTime = Date.now()
  console.log('🚀 Admin Dashboard: Starting data fetch...')
  
  const admin = await getAdmin()
  
  if (!admin) {
    redirect('/admin/login')
  }

  const resolvedSearchParams = await searchParams
  const activeTab = resolvedSearchParams.tab || 'doctors'
  
  // Parallelize independent data fetching calls
  const dataFetchStart = Date.now()
  const [statsResult, doctorsResult] = await Promise.all([
    getAdminDashboardStats(),
    getAllDoctorsWithStats()
  ])
  const dataFetchEnd = Date.now()
  
  console.log(`⚡ Admin Dashboard: Data fetch completed in ${dataFetchEnd - dataFetchStart}ms (Total: ${dataFetchEnd - startTime}ms)`)
  
  const stats = statsResult.success ? statsResult.data : null
  const doctors = doctorsResult.success ? doctorsResult.data || [] : []

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminDashboardHeader admin={admin} />
      
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Dashboard Stats */}
        {stats && (
          <div className="mb-8">
            <AdminDashboardStats stats={stats} />
          </div>
        )}

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <a
                href="/admin/dashboard?tab=doctors"
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'doctors'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300'
                }`}
              >
                Doctor Management
              </a>
              <a
                href="/admin/dashboard?tab=billing"
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'billing'
                    ? 'border-teal-500 text-teal-600'
                    : 'border-transparent text-slate-800 hover:text-slate-900 hover:border-gray-300'
                }`}
              >
                Billing & Referrals
              </a>
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'doctors' && (
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Doctor Management
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Manage doctor accounts, approvals, and quotas
              </p>
            </div>
            
            <DoctorsTable doctors={doctors} />
          </div>
        )}

        {activeTab === 'billing' && (
          <BillingManagement />
        )}
      </main>
    </div>
  )
}